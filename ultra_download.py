#!/usr/bin/env python3
"""
使用ultralytics自动下载权重
"""

from ultralytics import YOLO
import os
import shutil
import torch

def download_weights():
    """下载权重文件"""
    print("🚀 使用ultralytics下载YOLO权重")
    print("=" * 40)
    
    # 确保目录存在
    os.makedirs("./(f)models", exist_ok=True)
    
    weights = ['yolov8n.pt', 'yolov8s.pt']
    
    for weight_name in weights:
        try:
            print(f"\n📥 下载 {weight_name}...")
            
            # 删除可能存在的旧文件
            target_path = f"./(f)models/{weight_name}"
            if os.path.exists(target_path):
                os.remove(target_path)
                print(f"   🗑️  删除旧文件")
            
            # 使用YOLO下载 (会自动下载到缓存)
            model = YOLO(weight_name)
            
            # 查找下载的文件
            # 检查当前目录
            if os.path.exists(weight_name):
                shutil.move(weight_name, target_path)
                print(f"   ✅ 移动到目标位置: {target_path}")
            else:
                # 检查缓存目录
                from pathlib import Path
                cache_paths = [
                    Path.home() / '.cache' / 'ultralytics' / weight_name,
                    Path.home() / '.ultralytics' / weight_name,
                    Path(weight_name)
                ]
                
                found = False
                for cache_path in cache_paths:
                    if cache_path.exists():
                        shutil.copy2(cache_path, target_path)
                        print(f"   ✅ 从缓存复制: {cache_path}")
                        found = True
                        break
                
                if not found:
                    print(f"   ❌ 未找到下载的文件")
                    continue
            
            # 验证文件
            if os.path.exists(target_path):
                size_mb = os.path.getsize(target_path) / (1024 * 1024)
                print(f"   📊 文件大小: {size_mb:.1f} MB")
                
                # 验证PyTorch模型
                try:
                    checkpoint = torch.load(target_path, map_location='cpu')
                    print(f"   ✅ 权重验证成功")
                    if 'model' in checkpoint:
                        print(f"   📋 模型类型: {type(checkpoint['model'])}")
                except Exception as e:
                    print(f"   ❌ 权重验证失败: {e}")
            
        except Exception as e:
            print(f"❌ {weight_name} 下载失败: {e}")
    
    print(f"\n🎯 下载完成!")
    
    # 最终检查
    print("\n📋 最终检查:")
    for weight_name in weights:
        target_path = f"./(f)models/{weight_name}"
        if os.path.exists(target_path):
            size_mb = os.path.getsize(target_path) / (1024 * 1024)
            print(f"✅ {weight_name}: {size_mb:.1f} MB")
        else:
            print(f"❌ {weight_name}: 不存在")

if __name__ == "__main__":
    download_weights()
